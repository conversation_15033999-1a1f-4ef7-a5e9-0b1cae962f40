"""Main FastAPI application for LLM Proxy Server"""

import asyncio
import json
import time
from contextlib import asynccontextmanager
from typing import Dict, Optional

import structlog
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from . import __version__
from .auth import AuthManager
from .config import get_settings
from .load_balancer import LoadBalancer
from .models import *
from .monitoring import MetricsManager
from .proxy_manager import ProxyManager

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Global instances
settings = get_settings()
auth_manager = AuthManager(settings.auth_config_path)
load_balancer = LoadBalancer(settings.load_balancer_strategy)
metrics_manager = MetricsManager()
proxy_manager = ProxyManager(settings, load_balancer, metrics_manager)

# Security
security = HTTPBearer(auto_error=False)


async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Dict:
    """Get current authenticated user"""
    if not settings.auth_enabled:
        return {"username": "anonymous", "is_admin": False}
    
    if not credentials:
        raise HTTPException(status_code=401, detail="Authentication required")
    
    user = await auth_manager.authenticate(credentials.credentials)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid authentication")
    
    # Log the request
    logger.info(
        "Request authenticated",
        user=user["username"],
        endpoint=request.url.path,
        method=request.method,
        client_ip=request.client.host if request.client else "unknown"
    )
    
    return user


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    logger.info("Starting LLM Proxy Server", version=__version__)
    
    # Initialize proxy manager
    await proxy_manager.initialize()
    
    # Start background tasks
    health_check_task = asyncio.create_task(health_check_loop())
    
    yield
    
    # Cleanup
    health_check_task.cancel()
    await proxy_manager.shutdown()
    logger.info("LLM Proxy Server stopped")


async def health_check_loop():
    """Background health check loop"""
    while True:
        try:
            await asyncio.sleep(settings.health_check_interval)
            # Perform health checks on all hosts
            # This would be implemented in proxy_manager
        except asyncio.CancelledError:
            break
        except Exception as e:
            logger.error("Health check failed", error=str(e))


# Create FastAPI app
app = FastAPI(
    title="LLM Proxy Server",
    description="A load-balancing proxy server for Ollama instances",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """Log all requests"""
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    
    logger.info(
        "Request processed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
        client_ip=request.client.host if request.client else "unknown"
    )
    
    return response


# Ollama API endpoints
@app.post("/api/chat")
async def chat(
    request: ChatRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle chat requests with streaming support"""
    start_time = time.time()
    
    try:
        metrics_manager.record_request("chat", request.model, "proxy")
        
        if request.stream:
            async def stream_chat():
                async for chunk in proxy_manager.handle_chat_stream(request, user):
                    yield f"{json.dumps(chunk.dict())}\n"
            
            return StreamingResponse(
                stream_chat(),
                media_type="application/x-ndjson",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            response = await proxy_manager.handle_chat(request, user)
            metrics_manager.record_response_time("chat", request.model, "proxy", time.time() - start_time)
            return response
            
    except Exception as e:
        metrics_manager.record_error("chat", request.model, "proxy", type(e).__name__)
        logger.error("Chat request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/generate")
async def generate(
    request: GenerateRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle generate requests with streaming support"""
    start_time = time.time()
    
    try:
        metrics_manager.record_request("generate", request.model, "proxy")
        
        if request.stream:
            async def stream_generate():
                async for chunk in proxy_manager.handle_generate_stream(request, user):
                    yield f"{json.dumps(chunk.dict())}\n"
            
            return StreamingResponse(
                stream_generate(),
                media_type="application/x-ndjson",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            response = await proxy_manager.handle_generate(request, user)
            metrics_manager.record_response_time("generate", request.model, "proxy", time.time() - start_time)
            return response
            
    except Exception as e:
        metrics_manager.record_error("generate", request.model, "proxy", type(e).__name__)
        logger.error("Generate request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/embed")
async def embed(
    request: EmbedRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle embed requests"""
    try:
        response = await proxy_manager.handle_embed(request, user)
        return response
    except Exception as e:
        logger.error("Embed request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/show")
async def show(
    request: ShowRequest,
    user: Dict = Depends(get_current_user)
):
    """Handle show requests"""
    try:
        response = await proxy_manager.handle_show(request, user)
        return response
    except Exception as e:
        logger.error("Show request failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/tags")
async def list_models(user: Dict = Depends(get_current_user)) -> ModelsResponse:
    """List all available models"""
    try:
        models = await proxy_manager.list_models()
        return ModelsResponse(models=models)
    except Exception as e:
        logger.error("List models failed", error=str(e), user=user.get("username"))
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/version")
async def version():
    """Get API version"""
    return {"version": "0.5.4"}  # Ollama compatibility


# Proxy management endpoints
@app.get("/proxy/status")
async def get_status(user: Dict = Depends(get_current_user)) -> ProxyStatus:
    """Get proxy server status"""
    if not user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Admin access required")
    
    return await proxy_manager.get_status()


@app.get("/proxy/metrics")
async def get_metrics(user: Dict = Depends(get_current_user)):
    """Get proxy metrics"""
    if not user.get("is_admin", False):
        raise HTTPException(status_code=403, detail="Admin access required")
    
    return metrics_manager.generate_metrics()


@app.get("/proxy/health")
async def health_check() -> HealthResponse:
    """Health check endpoint"""
    health_status = metrics_manager.get_health_status()
    
    return HealthResponse(
        status=health_status["status"],
        timestamp=datetime.now(),
        version="1.0.0",
        uptime=health_status["uptime"]
    )


@app.get("/")
async def root():
    """Root endpoint - Ollama compatibility"""
    return {"message": "Ollama is running"}


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "llm_proxy_server.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )